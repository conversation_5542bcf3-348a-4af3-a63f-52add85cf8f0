using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;

public class CommonUnitBehaviour : CommonBehaviour, MoraleV2Manager.IMoraleEntity
{
	protected enum BloodType
	{
		None,
		Poison,
		Death
	}

	private class SmallExplosionInfo
	{
		public Aoe.Template Template { get; private set; }

		public float Chance { get; private set; }

		public bool BurningDisintegration { get; private set; }

		public float OffsetX { get; private set; }

		public SmallExplosionInfo(Aoe.Template template, float chance, bool burningDisintegration = false, float offsetX = 0f)
		{
			Template = template;
			Chance = chance;
			BurningDisintegration = burningDisintegration;
			OffsetX = offsetX;
		}
	}

	private static readonly Dictionary<CreatureTemplate.CreatureType, SmallExplosionInfo> UnitsWithSmallExplosionDeath = new Dictionary<CreatureTemplate.CreatureType, SmallExplosionInfo>
	{
		{
			CreatureTemplate.CreatureType.h_welder,
			new SmallExplosionInfo(Aoe.Template.SmallFireExplosion, 100f)
		},
		{
			CreatureTemplate.CreatureType.h_flamethrower,
			new SmallExplosionInfo(Aoe.Template.SmallFireExplosion, 100f)
		},
		{
			CreatureTemplate.CreatureType.z_firefighter,
			new SmallExplosionInfo(Aoe.Template.FirefighterExplosion, 100f)
		},
		{
			CreatureTemplate.CreatureType.h_firefighter,
			new SmallExplosionInfo(Aoe.Template.SmallFireExplosion, 50f, true)
		},
		{
			CreatureTemplate.CreatureType.z_chineseExpl,
			new SmallExplosionInfo(Aoe.Template.SmallFireExplosion, 100f)
		},
		{
			CreatureTemplate.CreatureType.h_grenader,
			new SmallExplosionInfo(Aoe.Template.SmallFireExplosion, 0f, false, 38f)
		}
	};

	private CreatureInstance creature;

	private FxHealthBar healthBar;

	private FxCritEffect fxCritEffect;

	private float spawnBarOffset;

	private UnitMover unitMover = new UnitMover();

	private HashSet<Func<int>> agilityModifiers = new HashSet<Func<int>>();

	private HashSet<Func<int>> aggressionModifiers = new HashSet<Func<int>>();

	private List<ICommonBehaviour> aoeTargets = new List<ICommonBehaviour>();

	private Coroutine delayedFreezeDebuffCancel;

	private float? immortalResurrectTimeEnd;

	private TimelessWhileChecker mainCoroutineWhileChecker = new TimelessWhileChecker();

	private float pullByStormDelta;

	private Tween pullByStormTween;

	bool MoraleV2Manager.IMoraleEntity.IsTeammatesDebuffer
	{
		get
		{
			return UnitsManager.IsTeammatesDebuffer(CreatureType);
		}
	}

	int MoraleV2Manager.IMoraleEntity.MoralePosition
	{
		get
		{
			return (int)base.XZ.x;
		}
	}

	int MoraleV2Manager.IMoraleEntity.MoraleInitial
	{
		get
		{
			return creature.Template.MoraleV2.InitialMoral;
		}
	}

	int MoraleV2Manager.IMoraleEntity.MoraleAura
	{
		get
		{
			return creature.Template.MoraleV2.BDMoral;
		}
	}

	int MoraleV2Manager.IMoraleEntity.MoraleAuraLeft
	{
		get
		{
			return (Targeter.MoveDirectionX <= 0) ? creature.Template.MoraleV2.MoralDistanceForward : creature.Template.MoraleV2.MoralDistanceBack;
		}
	}

	int MoraleV2Manager.IMoraleEntity.MoraleAuraRight
	{
		get
		{
			return (Targeter.MoveDirectionX >= 0) ? creature.Template.MoraleV2.MoralDistanceForward : creature.Template.MoraleV2.MoralDistanceBack;
		}
	}

	public BattleUnit BattleUnit { get; protected set; }

	protected int ClipIdle { get; set; }

	protected bool IsNextAttackCritical { get; set; }

	protected int ClipMeleeCritical { get; set; }

	protected bool DenyFireDisintegration { get; set; }

	protected bool FreezeDebuffActive { get; private set; }

	protected bool HaveIdle
	{
		get
		{
			return ClipIdle != 0 && Creature.Template.IdleCount != 0;
		}
	}

	protected bool CanRest
	{
		get
		{
			return HaveIdle;
		}
	}

	public bool HaveResistForFreezeDebuff { get; set; }

	protected bool DenyStartMainCoroutineOnInit { get; set; }

	public override Vector3 Scale
	{
		get
		{
			return BattleUnit.SpriteScale;
		}
		set
		{
			BattleUnit.SpriteScale = value;
		}
	}

	protected bool IsImmortal
	{
		get
		{
			float? num = immortalResurrectTimeEnd;
			int result;
			if (num.HasValue)
			{
				float? num2 = immortalResurrectTimeEnd;
				result = ((num2.HasValue && Time.fixedTime < num2.GetValueOrDefault()) ? 1 : 0);
			}
			else
			{
				result = 0;
			}
			return (byte)result != 0;
		}
	}

	public override CreatureInstance Creature
	{
		get
		{
			return creature;
		}
		protected set
		{
			creature = value;
			PositionChecker.CheckBehaviourInClicableZoneLocating(this);
		}
	}

	public override CreatureTemplate.CreatureType CreatureType
	{
		get
		{
			return creature.Template.UnitId;
		}
	}

	public CreatureTemplate.CreatureType VisualType { get; set; }

	public UnitRandomizer Randomizer { get; protected set; }

	protected List<int> MeleeAttackClips { get; set; }

	protected UnitMover UnitMover
	{
		get
		{
			return unitMover;
		}
		set
		{
			unitMover = value;
		}
	}

	protected float ExtraMoveX { get; set; }

	protected ICommonBehaviour Target
	{
		get
		{
			return unitMover.Target;
		}
		private set
		{
			unitMover.Target = value;
		}
	}

	protected bool IsAttackingMeleeTarget
	{
		get
		{
			return Targeter.IsAttackingMeleeTarget;
		}
		set
		{
			Targeter.IsAttackingMeleeTarget = value;
		}
	}

	public FxHealthBar HealthBar
	{
		get
		{
			return healthBar;
		}
	}

	public int SpawnLookDirectionX { get; protected set; }

	protected CreatureTemplate.CreatureType ResurrectFrom { get; set; }

	public UnitTargeter Targeter { get; private set; }

	public HashSet<Func<int>> AggressionModifiers
	{
		get
		{
			return aggressionModifiers;
		}
	}

	public int FatalDamageDirectionX { get; private set; }

	public UnitTeam FatalDamageDealerTeam { get; private set; }

	public DamageType FatalDamageType { get; private set; }

	public int ResurrectCounter { get; private set; }

	public virtual bool CanBeFrozen
	{
		get
		{
			return true;
		}
	}

	protected virtual int ClipSpecial
	{
		get
		{
			return BattleUnit.GetClip(ClipData.HashSpecial);
		}
	}

	public int PositionDirectionOffsetOnDeath
	{
		get
		{
			if (GameController.Instance.CorpseManager.UnitIgnoresFatalDamageDirection(VisualType))
			{
				return -Targeter.MoveDirectionX;
			}
			return -FatalDamageDirectionX;
		}
	}

	protected float SpawnBarOffset
	{
		get
		{
			return (spawnBarOffset != 0f) ? spawnBarOffset : (spawnBarOffset = CreaturesOffsets.GetSpawnBarOffset(VisualType));
		}
		private set
		{
			spawnBarOffset = value;
		}
	}

	public float UnitButtonOffset
	{
		get
		{
			return SpawnBarOffset + 15f;
		}
	}

	protected bool IsEnemyBaseReached
	{
		get
		{
			if (Targeter.MoveDirectionX < 0)
			{
				return base.XYZ.x <= GameController.Instance.LeftTeam.Base.Right;
			}
			return base.XYZ.x >= GameController.Instance.RightTeam.Base.Left;
		}
	}

	protected bool IsInsideOwnBase
	{
		get
		{
			if (Targeter.MoveDirectionX < 0)
			{
				return base.XYZ.x >= GameController.Instance.RightTeam.Base.Left;
			}
			return base.XYZ.x <= GameController.Instance.LeftTeam.Base.Right;
		}
	}

	public int Agility
	{
		get
		{
			if (FreezeDebuffActive)
			{
				return 0;
			}
			int num = 0;
			foreach (Func<int> agilityModifier in agilityModifiers)
			{
				num += agilityModifier();
			}
			int value = creature.Agility + num;
			return Mathf.Clamp(value, 0, 100);
		}
	}

	public int Aggression
	{
		get
		{
			if (FreezeDebuffActive)
			{
				return 0;
			}
			int num = 0;
			foreach (Func<int> aggressionModifier in aggressionModifiers)
			{
				num += aggressionModifier();
			}
			int value = creature.Aggression + num;
			return Mathf.Clamp(value, 0, 100);
		}
	}

	public override bool IsDead
	{
		get
		{
			return creature.HealthPoints.IsEmpty || base.Disintegrated || this == null;
		}
	}

	public event Action FreezeDebuffActiveChanged = delegate
	{
	};

	public event Action<CommonUnitBehaviour> RanAwayFromBattleField = delegate
	{
	};

	public override void Destroy()
	{
		Creature.Speed.KillStormTween();
		TweenUtils.KillAndNull(ref pullByStormTween);
		UnityEngine.Object.Destroy(base.gameObject);
		base.Destroy();
	}

	public override int GetAttackFreeze(DamageType damageType)
	{
		if (!creature.Template.Freeze.ContainsKey(damageType))
		{
			return 0;
		}
		return creature.Template.Freeze[damageType];
	}

	public override bool Attacked(ICommonBehaviour dealer, int damage, DamageType dmgType, int dmgDirX, AttackOptions options)
	{
		if (IsDead && FatalDamageDirectionX == 0)
		{
			FatalDamageDirectionX = dmgDirX;
			FatalDamageType = dmgType;
			FatalDamageDealerTeam = ((dealer != null) ? dealer.Team : UnitTeam.None);
		}
		return base.Attacked(dealer, damage, dmgType, dmgDirX, options);
	}

	protected virtual void OnDisable()
	{
		if (GameController.Instance != null)
		{
			GameController.Instance.UnitRegister.UnregisterUnit(this);
		}
	}

	protected virtual void Awake()
	{
		Targeter = new UnitTargeter();
		ExtraMoveX = UnityEngine.Random.Range(0, 9);
	}

	protected void CheckSlowdownBySandstorm()
	{
		Creature.Speed.TrySlowdownByStorm(Targeter.MoveDirectionX, Creature.Template.PushInfo);
	}

	public void ApplyPoisonEffect()
	{
		BattleUnit.SetShieldEffects(true);
		healthBar.ChangeDefaultColor(Colors.PoisonColor);
	}

	protected virtual bool SetTarget(ICommonBehaviour newTarget)
	{
		if (newTarget != Target)
		{
			Target = newTarget;
			IsAttackingMeleeTarget = false;
			return true;
		}
		return false;
	}

	protected void ClearTarget()
	{
		Target = null;
		IsAttackingMeleeTarget = false;
	}

	private int GetDamageForTarget(ICommonBehaviour target)
	{
		return creature.MeleeDamage.Value;
	}

	protected IEnumerator ProcessResurrect(int resClip)
	{
		float immortalTimeSeconds = BattleParams.Instance.ImmortalTimeAfterResurrectSeconds;
		if (immortalTimeSeconds > 0f)
		{
			immortalResurrectTimeEnd = Time.fixedTime + immortalTimeSeconds;
		}
		BattleUnit.PlayClip(resClip);
		BattleUnit.SpriteScale = new Vector3(SpawnLookDirectionX, 1f, 1f);
		do
		{
			yield return WaitFor.FixedUpdate;
		}
		while (!IsDead && !BattleUnit.IsClipEnded);
		UnitDataScriptableObject unitData = GameController.Instance.AssetBundleLoaderUnitData.LoadBundle(VisualType);
		Sprite decalSprite = unitData.SpritesAfterSpawn.Find((Sprite o) => o.name.EndsWith(ResurrectFrom.ToString()));
		GameController.Instance.CorpseManager.SpawnDecal(VisualType, CorpseManager.Decal.Spawn, base.XYZ, SpawnLookDirectionX, decalSprite);
		float spawnShift = Creature.Template.SpawnShift * (float)(-SpawnLookDirectionX);
		base.XYZ += new Vector3(spawnShift, 0f, 0f);
        ClearTarget();
	}

	protected IEnumerator CoIdle(int clip)
	{
		if (!HaveIdle)
		{
			yield return WaitFor.FixedUpdate;
			yield break;
		}
		for (int i = 0; i < Creature.Template.IdleCount; i++)
		{
			IEnumerator e = CoPlayClipOnce(clip);
			while (e.MoveNext())
			{
				yield return e.Current;
			}
			if (IsDead)
			{
				break;
			}
		}
	}

	protected IEnumerator CoIdle(int clip, Func<bool> extraStopCondition)
	{
		if (!HaveIdle)
		{
			yield return WaitFor.FixedUpdate;
			yield break;
		}
		for (int i = 0; i < Creature.Template.IdleCount; i++)
		{
			IEnumerator e = CoPlayClipOnce(clip);
			while (e.MoveNext())
			{
				yield return e.Current;
			}
			if (IsDead || extraStopCondition())
			{
				break;
			}
		}
	}

	protected IEnumerator CoPlayClipOnce(int clip, BattleUnit.AnimationEventDelegate onEvent = null)
	{
		BattleUnit.PlayClip(clip, onEvent);
		while (!IsDead)
		{
			yield return WaitFor.FixedUpdate;
			if (BattleUnit.IsClipEnded)
			{
				break;
			}
		}
		if (onEvent != null)
		{
			BattleUnit.ClearEventDelegate(clip);
		}
	}

	public void PullByStorm()
	{
		TweenUtils.KillAndNull(ref pullByStormTween);
		pullByStormDelta = 0f;
		if (base.IsFrozen)
		{
			return;
		}
		float num = Creature.Template.PushInfo.ApplyResistTo(8f);
		float duration = Creature.Template.PushInfo.ApplyResistTo(0.2f);
		if (!(num <= 0f))
		{
			pullByStormTween = DOTween.To(() => pullByStormDelta, delegate(float v)
			{
				float x = v - pullByStormDelta;
				base.XYZ -= new Vector3(x, 0f, 0f);
				pullByStormDelta = v;
			}, num, duration).OnKill(delegate
			{
				pullByStormTween = null;
			});
		}
	}

	protected virtual IEnumerator CoBeforeMeleeAttack(int clipWait)
	{
		if (CanRest && Aggression <= UnityEngine.Random.Range(0, 100))
		{
			IEnumerator e = CoDoActionBeforeAttack(clipWait);
			while (e.MoveNext())
			{
				yield return e.Current;
			}
		}
	}

	protected virtual IEnumerator CoDoActionBeforeAttack(int clipWait)
	{
		IEnumerator e2 = CoIdle(clipWait);
		while (e2.MoveNext())
		{
			yield return e2.Current;
		}
		if (IsDead)
		{
			yield break;
		}
		while (Aggression == 0)
		{
			e2 = CoPlayClipOnce(clipWait);
			while (e2.MoveNext())
			{
				yield return e2.Current;
			}
			if (IsDead)
			{
				break;
			}
		}
	}

	protected virtual bool Attack(bool isCritical)
	{
		//Debug.LogError("Attack++++++++++++");
		if (Target != null && Target.IsDead)
		{
			return false;
		}
		bool result = false;
		DamageType dmgType = creature.MeleeDamage.Type;
		bool flag = creature.Template.AoeRadius > 0f && Targeter.CanMeleeAoeAttackTarget(Target);
		Action<AttackOptions> setOptions = delegate(AttackOptions o)
		{
			o[AttackOption.Critical] = isCritical;
		};
		Targeter.GetMeleeAoeTargets(aoeTargets);
		int moveDirectionX = Targeter.MoveDirectionX;
		if (flag)
		{
			foreach (ICommonBehaviour aoeTarget in aoeTargets)
			{
				if (AttackManager.DoAttack(this, aoeTarget, GetDamageForTarget(aoeTarget), dmgType, moveDirectionX, setOptions))
				{
					result = true;
				}
			}
			aoeTargets.Clear();
		}
		else
		{
			if (Target == null)
			{
				return false;
			}
			if (Targeter.IsTargetInMeleeZone(Target, 0f))
			{
				int damageForTarget = GetDamageForTarget(Target);
				result = AttackManager.DoAttack(this, Target, damageForTarget, dmgType, moveDirectionX, setOptions);
			}
		}
		return result;
	}

	protected virtual void InitAnimationClips()
	{
		MeleeAttackClips = new List<int>();
		int clip = BattleUnit.GetClip(ClipData.HashMelee);
		if (clip != 0)
		{
			MeleeAttackClips.Add(clip);
		}
		int num = 1;
		while (true)
		{
			clip = BattleUnit.GetClip("Melee" + num);
			if (clip != 0)
			{
				MeleeAttackClips.Add(clip);
			}
			else if (num >= 2)
			{
				break;
			}
			num++;
		}
		ClipMeleeCritical = BattleUnit.GetClip(ClipData.HashMeleeCritical);
	}

	protected int GetRandomMeleeAttackClip()
	{
		return MeleeAttackClips[UnityEngine.Random.Range(0, MeleeAttackClips.Count)];
	}

	protected virtual void SpawnDamageText(int dmg, DamageType dmgType, bool isCritical, ICommonBehaviour dealer)
	{
		FxDamageText fxDamageText = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxDamageText);
		if (dmgType == DamageType.Poison)
		{
			fxDamageText.ChangeColor(Colors.PoisonColor);
		}
		fxDamageText.transform.position = base.XYZ + new Vector3(UnityEngine.Random.Range(-10, 10), (float)UnityEngine.Random.Range(45, 60) + ApplySpriteOffsetY.Get(base.XYZ.z), -0.02f);
		fxDamageText.Init(dmg);
		if (isCritical)
		{
			fxDamageText.transform.localScale = new Vector3(1.5f, 1.5f, 1f);
		}
	}

	protected void SpawnBloodSplashAndCritText(int directionX)
	{
		FadingBattleFx fadingBattleFx = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxBloodSplash);
		fadingBattleFx.transform.position = base.XYZ + new Vector3(UnityEngine.Random.Range(-2, 8), (float)UnityEngine.Random.Range(41, 49) + ApplySpriteOffsetY.Get(base.XYZ.z), -0.01f);
		fadingBattleFx.transform.localScale = new Vector3(directionX, 1f, 1f);
		if (fxCritEffect == null)
		{
			fxCritEffect = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxCritEffext);
			fxCritEffect.transform.SetParent(base.transform, true);
			fxCritEffect.transform.localPosition = fxCritEffect.Offset;
		}
		fxCritEffect.Spawn();
	}

	protected void SpawnBloodShot(int directionX)
	{
		FadingBattleFx fadingBattleFx = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxBloodShot);
		fadingBattleFx.transform.localScale = new Vector3(-directionX, 1f, 1f);
		fadingBattleFx.transform.position = base.XYZ + new Vector3(UnityEngine.Random.Range(-10, 10), (float)UnityEngine.Random.Range(21, 29) + ApplySpriteOffsetY.Get(base.XYZ.z), -0.05f);
	}

	protected void SpawnSwatShot(int directionX)
	{
		FadingBattleFx fadingBattleFx = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxSwatShot);
		fadingBattleFx.transform.localScale = new Vector3(-directionX, 1f, 1f);
		fadingBattleFx.transform.position = base.XYZ + new Vector3(UnityEngine.Random.Range(-10, 10), (float)UnityEngine.Random.Range(21, 29) + ApplySpriteOffsetY.Get(base.XYZ.z), -0.05f);
	}

	protected void SpawnBlood(int directionX, BloodType type)
	{
		BattleEffects battleEffects = GameController.Instance.BattleEffects;
		FadingBattleFx fadingBattleFx = null;
		float num = 0f;
		float num2 = 0f;
		float num3 = -0.01f;
		float num4 = -1f;
		switch (type)
		{
		case BloodType.Poison:
			fadingBattleFx = battleEffects.FxPoisonBlood;
			num = UnityEngine.Random.Range(-2, 2);
			num2 = UnityEngine.Random.Range(13, 15);
			num4 = -directionX;
			break;
		case BloodType.Death:
			fadingBattleFx = battleEffects.FxDeathBlood;
			num = UnityEngine.Random.Range(5, 10);
			num2 = UnityEngine.Random.Range(-12, -8);
			num4 = directionX;
			break;
		default:
			fadingBattleFx = battleEffects.FxCommonBlood;
			num = UnityEngine.Random.Range(-22, 22);
			num2 = UnityEngine.Random.Range(21, 29);
			num4 = -directionX;
			break;
		}
		float x = num + base.XYZ.x;
		float y = num2 + ApplyFixedSpriteOffsetY.Get(base.XYZ.z);
		float z = num3 + base.XYZ.z;
		FadingBattleFx fxCommonBlood = UnityEngine.Object.Instantiate(fadingBattleFx);
		fxCommonBlood.transform.SetParent(GameController.Instance.BloodHolder.transform, true);
		fxCommonBlood.transform.position = new Vector3(x, y, z);
		fxCommonBlood.transform.localScale = new Vector3(num4, 1f, 1f);
		AnimationEventHandler animationHandler = fxCommonBlood.AnimationHandler;
		animationHandler.AnimationFreeze = (AnimationEventHandler.AnimationEventDelegate)Delegate.Combine(animationHandler.AnimationFreeze, (AnimationEventHandler.AnimationEventDelegate)delegate
		{
			ApplyParentByCategory component = fxCommonBlood.gameObject.GetComponent<ApplyParentByCategory>();
			if (component != null)
			{
				component.enabled = true;
			}
		});
	}

	void MoraleV2Manager.IMoraleEntity.ApplyMoraleDebuff(bool applyDebuff)
	{
		if (applyDebuff)
		{
			agilityModifiers.Add(MoralDebuffAgilityModifier);
			aggressionModifiers.Add(MoralDebuffAggressionModifier);
		}
		else
		{
			agilityModifiers.Remove(MoralDebuffAgilityModifier);
			aggressionModifiers.Remove(MoralDebuffAggressionModifier);
		}
		OnMoralDebuffApplied(applyDebuff);
	}

	private int MoralDebuffAgilityModifier()
	{
		return (int)((float)(-creature.Agility) * creature.Template.MoraleV2.AgilityPenalty);
	}

	private int MoralDebuffAggressionModifier()
	{
		return (int)((float)(-creature.Aggression) * creature.Template.MoraleV2.AggressionPenalty);
	}

	protected bool IsAwayFromBattlefield()
	{
		float x = base.XYZ.x;
		if (Targeter.MoveDirectionX > 0)
		{
			return x > 484f;
		}
		return x < -482f;
	}

	protected void RaiseRunAwayEvent()
	{
		this.RanAwayFromBattleField(this);
	}

	protected virtual void Die()
	{
		if (!DenyFireDisintegration && !base.Disintegrated && FatalDamageType == DamageType.Fire)
		{
			MarkDisintegrated();
			BattleUnit.SetBurningDisintegation();
		}
		Randomizer.GiveRageOnDeath();
		Randomizer.UpdateVisualBonusEffect();
		if (BattleUnit != null)
		{
			BattleUnit.SetShieldEffects(false);
			BattleUnit.DestroyCollider();
		}
		Events.Instance.RaiseUnitKilled(this);
		GameController.Instance.UnitRegister.UnregisterUnit(this);
		SmallExplosionInfo value;
		if (UnitsWithSmallExplosionDeath.TryGetValue(CreatureType, out value) && (float)UnityEngine.Random.Range(0, 100) < value.Chance)
		{
			SmallExplosionDeath(CreatureType);
		}
	}

	protected bool NeedSparkSpawn(DamageType dmgType)
	{
		float resist = GetResist(dmgType);
		return dmgType == DamageType.Range && resist >= 0.5f;
	}

	protected void SpawnDamageEffect(ICommonBehaviour dealer, int damageDirX, int damageValue, DamageType dmgType, bool needSparkSpawn)
	{
		BloodType bloodType = ((dmgType == DamageType.Poison) ? BloodType.Poison : BloodType.None);
		bool flag = false;
		switch (dmgType)
		{
		case DamageType.Range:
			if (needSparkSpawn)
			{
				SpawnSpark(damageDirX);
				break;
			}
			if (dealer.CreatureType == CreatureTemplate.CreatureType.h_swat && dmgType == DamageType.Range)
			{
				SpawnSwatShot(damageDirX);
			}
			else
			{
				SpawnBloodShot(damageDirX);
			}
			flag = true;
			break;
		case DamageType.ImmortalBoss:
			SpawnSpark(damageDirX);
			break;
		default:
			flag = true;
			break;
		}
		if (flag && (damageValue > 0 || bloodType == BloodType.None))
		{
			if (IsDead)
			{
				bloodType = BloodType.Death;
			}
			SpawnBlood(damageDirX, bloodType);
		}
	}

	private void SpawnSpark(int directionX)
	{
		FadingBattleFx fadingBattleFx = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxBigSpark);
		fadingBattleFx.transform.localScale = new Vector3(directionX, 1f, 1f);
		fadingBattleFx.transform.position = base.XYZ + new Vector3(UnityEngine.Random.Range(-2, 2), (float)UnityEngine.Random.Range(30, 40) + ApplySpriteOffsetY.Get(base.XYZ.z), -0.03f);
	}

	public override void SpawnerInitialization(Spawner.SpawnInfo info)
	{
		base.SpawnerInitialization(info);
		VisualType = info.VisualType;
		ResurrectCounter = info.ResurrectCounter;
		float num3 = (base.Depth = (base.Width = info.BattleUnit.CapsuleCollider.radius * 2f));
	}

	protected void CreateHealthBar()
	{
		if (object.ReferenceEquals(healthBar, null))
		{
			if (SpawnBarOffset == 0f)
			{
				throw new InvalidOperationException("SpawnBarOffset is 0 for unit " + CreatureType);
			}
			healthBar = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxHealthBar);
			healthBar.transform.SetParent(BattleUnit.SpriteContainer.transform, false);
			healthBar.transform.localPosition = new Vector3(0f, SpawnBarOffset, -0.01f);
			healthBar.Init(Creature.HealthPoints, CreatureType == CreatureTemplate.CreatureType.z_cephalopoda, base.Team == UnitTeam.Left && GameController.Settings.Mode == GameController.Mode.PvP);
			healthBar.ShowHealthWhenFull = GameController.Settings.Mode == GameController.Mode.PvP;
			if (GameController.Settings.Mode == GameController.Mode.PvP && UnitsManager.IsHumanPerson(CreatureType))
			{
				healthBar.CreatePVPBadge(CreatureType, base.Team);
			}
		}
	}

	public void ConvertTo(CreatureTemplate.CreatureType unitId, float offsetZ = 0f)
	{
		MarkDisintegrated();
		UnitTeam unitTeam = UnitTeam.None;
		switch (GameController.Settings.Mode)
		{
		case GameController.Mode.Campaign:
			unitTeam = CorpseManager.GetSpawnTeamCampaignMission(unitId);
			break;
		case GameController.Mode.PvP:
			unitTeam = CorpseManager.GetSpawnTeamPVPMission(unitId, base.Team);
			break;
		case GameController.Mode.WallMart:
		case GameController.Mode.Xmas:
		case GameController.Mode.Rage:
			unitTeam = CorpseManager.GetSpawnTeamCommonMission(unitId);
			break;
		case GameController.Mode.CornFarm:
			unitTeam = CorpseManager.GetSpawnTeamBanditsMission(unitId);
			break;
		default:
			unitTeam = UnitTeam.None;
			break;
		}
		GameController.Instance.GetTeam(unitTeam).Spawner.SpawnUnit(unitId, base.XZ.x, base.XZ.y + offsetZ, Targeter.MoveDirectionX, Colors.DefaultUnitColor, CreatureType, ResurrectCounter);
		RaiseDied();
		Destroy();
	}

	protected IEnumerator CoDie(int clipDeath)
	{
		if (!base.Disintegrated)
		{
			CreatureTemplate.CreatureType creatureType = BattleParams.Instance.DeadConversion.TryConvert(creature.Template.UnitId);
			if (creatureType != 0)
			{
				ConvertTo(creatureType);
				yield break;
			}
		}
		if (clipDeath != 0)
		{
			BattleUnit.SpriteScale = GetDieAnimationScaleX();
			BattleUnit.PlayClip(clipDeath, OnDieAnimEvent);
			do
			{
				yield return WaitFor.FixedUpdate;
			}
			while (!BattleUnit.IsClipEnded);
			OnDieAnimationComplete();
			BattleUnit.ClearEventDelegate(clipDeath);
		}
		// Debug.LogError($"死了--------------{BattleUnit.Behaviour.CreatureType},{BattleUnit.Behaviour.Team}");
		// Debug.LogError("---------" + BattleUnit.Behaviour.CreatureType);
		if (BattleUnit.Behaviour.Team == UnitTeam.Right)
		{
			yield return BattleUnit.DoFadeOut();
			// Destroy();
		}
		else
		{	
			RaiseDied();
		}
	}

	protected void SmallExplosionDeath(CreatureTemplate.CreatureType creature)
	{
		if (BattleUnit != null)
		{
			BattleUnit.StopAllMoraleIndications();
		}
		Vector3 vector = new Vector3((float)FatalDamageDirectionX * UnitsWithSmallExplosionDeath[creature].OffsetX, 0f, 0f);
		GameController.Instance.AoeTemplates.Spawn(UnitsWithSmallExplosionDeath[creature].Template, base.XYZ + vector);
		SoundManager.Instance.TryPlayOn(delegate(SoundsBattle m)
		{
			m.SmallExplosion(BattleUnit.AudioSource);
		});
		MarkDisintegrated();
		if (!DenyFireDisintegration && UnitsWithSmallExplosionDeath[creature].BurningDisintegration)
		{
			BattleUnit.SetBurningDisintegation();
		}
	}

	protected void CheckTimelessWhileInMainCoroutine()
	{
		mainCoroutineWhileChecker.CheckTimelessWhile(() => string.Concat(CreatureType, " ", GetType()));
	}

	protected virtual Vector3 GetDieAnimationScaleX()
	{
		return new Vector3(-FatalDamageDirectionX, 1f, 1f);
	}

	public override void SetFreezeEffect(bool isFreezeActive)
	{
		base.SetFreezeEffect(isFreezeActive);
		if (isFreezeActive)
		{
			if (delayedFreezeDebuffCancel != null)
			{
				StopCoroutine(delayedFreezeDebuffCancel);
				delayedFreezeDebuffCancel = null;
			}
		}
		else if (delayedFreezeDebuffCancel == null)
		{
			if (!HaveResistForFreezeDebuff)
			{
				FreezeDebuffActive = true;
				this.FreezeDebuffActiveChanged();
			}
			if (!IsDead)
			{
				delayedFreezeDebuffCancel = StartCoroutine(DelayedFreezeDebuffCancel());
			}
		}
	}

	private IEnumerator DelayedFreezeDebuffCancel()
	{
		float debuffTime = BattleParams.Instance.UnitFreezeDebuffTime;
		float cancellDebuffTime = Time.fixedTime + debuffTime;
		while (Time.fixedTime < cancellDebuffTime)
		{
			yield return WaitFor.FixedUpdate;
		}
		if (FreezeDebuffActive)
		{
			FreezeDebuffActive = false;
			this.FreezeDebuffActiveChanged();
		}
		delayedFreezeDebuffCancel = null;
	}

	protected virtual void OnMoralDebuffApplied(bool debuffApplied)
	{
	}

	protected virtual void OnDieAnimEvent()
	{
	}

	protected virtual void OnDieAnimationComplete()
	{
	}

	protected virtual void ResurrectActions()
	{
	}

	public virtual IEnumerable<ICommonBehaviour> GetTargets()
	{
		throw new Exception("GetTargets() is not implemented for " + Creature.Template.UnitId);
	}
}
