using System;
using System.Collections;
using System.Collections.Generic;
using System.Security.Policy;
using DG.Tweening;
using UnityEngine;
using static UnityEngine.UI.CanvasScaler;

public class BattleUnit : MonoBehaviour
{
    public delegate void AnimationEventDelegate();
    public delegate void AnimationEffectDelegate();

    public enum GlowType
    {
        Red,
        Blue
    }

    [SerializeField]
    private Animator animator;

    [SerializeField]
    private ApplySpriteOffsetY spriteContainer;

    [SerializeField]
    private AudioSource audioSource;

    [SerializeField]
    private CapsuleCollider capsuleCollider;

    [SerializeField]
    private AnimationEventHandler animationEventHandler;

    [SerializeField]
    private AnimationEffectHandler animationEffectHandler;

    [SerializeField]
    private ParticleSystem rageParticleSystem;

    [SerializeField]
    private ParticleSystem shieldParticleSystem;

    [SerializeField]
    private SpriteRenderer glow;

    [SerializeField]
    private Sprite redGlowSprite;

    [SerializeField]
    private Sprite blueGlowSprite;

    [SerializeField]
    private Color burningDarkest;

    [SerializeField]
    private Color burningInitial;

    [SerializeField]
    private RuntimeAnimatorController burningController;

    [SerializeField]
    private Vector3 blackSmokeOffset;

    [SerializeField]
    private BattleUnitEffects battleUnitEffects;

    [SerializeField]
    private Transform fearAnimationPlace;

    [SerializeField]
    private BattleUnitButton battleUnitButtonPrefab;

    private CommonBehaviour behaviour;

    private bool wasBurned;

    private bool isInAnimationEvent;

    private Dictionary<int, AnimationEventDelegate> eventDelegates = new Dictionary<int, AnimationEventDelegate>();

    private SpriteRenderer animatorSprite;
    private SkinnedMeshRenderer unitSkinMeshRenderer;

    private FxBuff rageEffect;

    private FxBlackSmoke blackSmoke;

    private bool denyChangeAnimatorColor;

    private FearAndRageAnimation fearAndRageAnimation;

    private BattleUnitButton battleUnitButtonInstance;

    private int? lastPlayClip;

    Dictionary<string, GameObject> unitEffectDict = new Dictionary<string, GameObject>();

    public Animator Animator
    {
        get
        {
            return animator;
        }
    }

    public SpriteRenderer AnimatorSprite
    {
        get
        {
            return animatorSprite;
        }
    }
    public SkinnedMeshRenderer UnitSkinMeshRenderer
    {
        get
        {
            return unitSkinMeshRenderer;
        }
    }

    public AnimationEventHandler AnimationHandler
    {
        get
        {
            return animationEventHandler;
        }
    }

    public AnimationEffectHandler AnimationEffectHandler
    {
        get
        {
            return animationEffectHandler;
        }
    }

    public ApplySpriteOffsetY SpriteContainer
    {
        get
        {
            return spriteContainer;
        }
    }

    public AudioSource AudioSource
    {
        get
        {
            return audioSource;
        }
    }

    public CapsuleCollider CapsuleCollider
    {
        get
        {
            return capsuleCollider;
        }
    }

    public CommonBehaviour Behaviour
    {
        get
        {
            return behaviour;
        }
    }

    public BattleUnitColorController ColorController { get; private set; }

    public Transform FearAnimationPlace
    {
        get
        {
            return fearAnimationPlace;
        }
    }

    private CreatureSpeed TrackingCreatureSpeed { get; set; }

    public Color BurningInitial
    {
        get
        {
            return burningInitial;
        }
        set
        {
            burningInitial = value;
        }
    }

    public Color BurningDarkest
    {
        get
        {
            return burningDarkest;
        }
        set
        {
            burningDarkest = value;
        }
    }

    public bool WasBurned
    {
        get
        {
            return wasBurned;
        }
        set
        {
            if (!value)
            {
                throw new Exception("Can't return was burned to false");
            }
            wasBurned = true;
        }
    }

    public Vector3 SpriteScale
    {
        get
        {
            return animator.transform.localScale;
        }
        set
        {
            animator.transform.localScale = value;
        }
    }

    public bool IsClipEnded
    {
        get
        {
            return animator.GetCurrentAnimatorStateInfo(0).normalizedTime >= 1f;
        }
    }

    private float AnimatorMovePPS
    {
        get
        {
            return animator.GetFloat(ClipData.HashMovePPS);
        }
        set
        {
            animator.SetFloat(ClipData.HashMovePPS, value);
        }
    }

    private GameObject unit3d;

    private void Awake()
    {
        animationEventHandler.AnimationEvent = OnAnimationEvent;
        if (animationEffectHandler != null)
            animationEffectHandler.AnimationEffect = OnEffect;
        ColorController = base.gameObject.AddComponent<BattleUnitColorController>();
        ColorController.Init(this);
    }

    private void OnDestroy()
    {
        animationEventHandler.AnimationEvent = null;
        if (animationEffectHandler != null)
            animationEffectHandler.AnimationEffect = null;
        if (Behaviour != null)
        {
            Behaviour.WasAttacked -= OnUnitAttacked;
            Behaviour.FreezeEffectChanged -= OnFreezeChanged;
        }
        if (TrackingCreatureSpeed != null)
        {
            TrackingCreatureSpeed.ValueChanged -= OnSpeedChanged;
            TrackingCreatureSpeed = null;
        }
    }

    private CreatureTemplate.CreatureType battleUnitType;

    public bool Init(CreatureTemplate.CreatureType unitType, Color overrideColor)
    {
        battleUnitType = unitType;
        UnitDataScriptableObject unitDataScriptableObject = GameController.Instance.AssetBundleLoaderUnitData.LoadBundle(unitType);
        RuntimeAnimatorController runtimeAnimatorController = ((!(unitDataScriptableObject != null)) ? null : unitDataScriptableObject.Animator);
        if (runtimeAnimatorController == null)
        {
            Debug.LogWarning("Animator controller not found: " + unitType);
            return false;
        }
        animator.runtimeAnimatorController = runtimeAnimatorController;
        animatorSprite = Animator.GetComponent<SpriteRenderer>();

        checkUnit3d();

        ColorController.SetBackgroundColor(overrideColor);
        if (unitType == CreatureTemplate.CreatureType.z_blackSkeleton)
        {
            SetShieldEffects(true);
        }
        BattleDebugEffects.Instance.ShowForUnitCollider(unitType, this);
        return true;
    }

    private bool checkUnit3d()
    {
        //if(battleUnitType.ToString()== "h_naked")
        if (true)
        {
            string prefab3dpath = string.Format(@"AssetBundles/battleunits3d/{0}/{0}_prefab", battleUnitType);
            //Debug.LogError("add battle unit===========>" + prefab3dpath);
            GameObject _prefab = (GameObject)Resources.Load(prefab3dpath);
            if (_prefab != null)
            {
                unit3d = Instantiate(_prefab);
                unit3d.transform.SetParent(spriteContainer.transform, false);
                // Debug.LogError("unit3dName---------" + unit3d.name);
                if (unit3d != null)
                {
                    animationEventHandler = unit3d.GetComponent<AnimationEventHandler>();
                    animationEventHandler.AnimationEvent = OnAnimationEvent;

                    animationEffectHandler = unit3d.GetComponent<AnimationEffectHandler>();
                    if (animationEffectHandler == null)
                    {
                        animationEffectHandler = unit3d.AddComponent<AnimationEffectHandler>();
                    }
                    animationEffectHandler.AnimationEffect = OnEffect;

                    //ColorController.Init(this);
                    //Debug.LogError(animationEventHandler.AnimationEvent);
                    animatorSprite.enabled = false;
                    animator = unit3d.GetComponent<Animator>();

                    BattleUnit3DSkinMeshRenderer busmr = unit3d.GetComponent<BattleUnit3DSkinMeshRenderer>();
                    if (busmr == null)
                    {
                        busmr = unit3d.AddComponent<BattleUnit3DSkinMeshRenderer>();
                        busmr.skinMeshRender = unit3d.GetComponentInChildren<SkinnedMeshRenderer>();
                        // Shader newShader = Shader.Find("Sprites/LerpColor");
                        Shader newShader = Shader.Find("UnityChan/Skin");
                        // Shader newShader = Shader.Find("AllIn1SpriteShader/AllIn1SpriteShader");
                        busmr.skinMeshRender.material.shader = newShader;

                        busmr.skinMeshRender.material.SetFloat("_EdgeThickness", 0.3f);
                    }
                    unitSkinMeshRenderer = busmr.skinMeshRender;
                    // ColorController.Init(this);
                    //animator.runtimeAnimatorController = runtimeAnimatorController;
                    // RuntimeAnimatorController runtimeAnimatorController3d = animator.runtimeAnimatorController;
                    // UnitDataScriptableObject unitDataScriptableObject3d = GameController.Instance.AssetBundleLoaderUnitData3d.LoadBundle(battleUnitType);
                    // RuntimeAnimatorController runtimeAnimatorController3d = ((!(unitDataScriptableObject3d != null)) ? null : unitDataScriptableObject3d.Animator);
                    // animator.runtimeAnimatorController = runtimeAnimatorController3d;
                    //unitEffectDict
                    Transform effectRoot = unit3d.transform.Find("3d/Effects");
                    if (effectRoot != null)
                    {
                        for (int i = 0; i < effectRoot.childCount; i++)
                        {
                            GameObject _effect = effectRoot.GetChild(i).gameObject;
                            _effect.SetActive(false);
                            unitEffectDict.Add(_effect.name, _effect);
                        }
                    }
                    Transform trans3d = unit3d.transform.Find("3d");
                    if (trans3d != null)
                    {
                        // Debug.LogError("修改一下3d节点的旋转");
                        trans3d.eulerAngles = new Vector3(0, 115, -8);
                    }
                    return true;

                }
            }
        }
        return false;
    }

    public IEnumerator DoFadeOut()
    {
        yield return null;
        BattleUnit3DSkinMeshRenderer busmr = unit3d.GetComponent<BattleUnit3DSkinMeshRenderer>();
        if (busmr != null)
        {
            Shader newShader = Shader.Find("AllIn1SpriteShader/AllIn1SpriteShader");
            busmr.skinMeshRender.material.shader = newShader;
            // newShader.
            //延迟消失
        } else {
            CommonUnitBehaviour commonUnitBehaviour = Behaviour as CommonUnitBehaviour;
            if (commonUnitBehaviour != null)
            {
                commonUnitBehaviour.Destroy();
            } 
        }
    }

    public void showSpawn()
    {
        if (unit3d != null)
        {
            string spawnpath = "AssetBundles/battleunits3d/spawn_prefab";
            //if (this.unit3d.transform.localScale.x > 0)
            //{
            //    Debug.LogError("特效方向反了"+gameObject.name);
            //    spawnpath = "AssetBundles/battleunits3d/spawn_flip_prefab";
            //}
            GameObject _spawnprefab = (GameObject)Resources.Load(spawnpath);
            GameObject _spawn = Instantiate(_spawnprefab);
            _spawn.GetComponent<SpawnEffect>().bindUnit(unit3d.transform);
            Transform spawnPos = unit3d.transform.Find("spawn");
            if (spawnPos == null)
            {
                spawnPos = unit3d.transform;
            }
            _spawn.transform.SetParent(spawnPos, false);
        }
    }

    public void TrackSpeed(CreatureSpeed speed)
    {
        if (TrackingCreatureSpeed != null)
        {
            TrackingCreatureSpeed.ValueChanged -= OnSpeedChanged;
            TrackingCreatureSpeed = null;
        }
        TrackingCreatureSpeed = speed;
        TrackingCreatureSpeed.ValueChanged += OnSpeedChanged;
        OnSpeedChanged();
    }

    private void OnSpeedChanged()
    {
        AnimatorMovePPS = TrackingCreatureSpeed.AnimatorMovePPS;
        Animator.speed = TrackingCreatureSpeed.AnimatorSpeed;
    }

    public void SetBonusRageEffects(bool isActive)
    {
        if (isActive)
        {
            if (rageEffect == null)
            {
                rageEffect = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxBuff);
                rageEffect.transform.SetParent(SpriteContainer.transform, false);
                rageEffect.Init(BuffManager.Type.EnhancedTeam, false, 0.47f);
            }
            rageParticleSystem.gameObject.SetActive(true);
            rageParticleSystem.Play();
        }
        else
        {
            if (rageEffect != null)
            {
                UnityEngine.Object.Destroy(rageEffect.gameObject);
            }
            Colors.HideParticles(rageParticleSystem, 0.5f, delegate
            {
                rageParticleSystem.Stop();
            });
        }
    }

    public void SetGlowEffect(bool isActive, GlowType glowType)
    {
        if (isActive)
        {
            switch (glowType)
            {
                case GlowType.Red:
                    glow.sprite = redGlowSprite;
                    break;
                case GlowType.Blue:
                    glow.sprite = blueGlowSprite;
                    break;
            }
        }
        glow.gameObject.SetActive(isActive);
    }

    public void SetShieldEffects(bool isActive)
    {
        if (isActive)
        {
            shieldParticleSystem.gameObject.SetActive(true);
            shieldParticleSystem.Play();
        }
        else
        {
            shieldParticleSystem.Stop();
        }
    }

    public int GetClip(string name)
    {
        return GetClip(Animator.StringToHash(name));
    }

    public int GetClip(int stateHash)
    {
        if (animator.HasState(0, stateHash))
        {
            if (stateHash == 0)
            {
                throw new UnityException("State hash is 0");
            }
            return stateHash;
        }
        return 0;
    }


    private void OnEffect(string effectStr)
    {
        // Debug.LogError("OnAnimationEffect+++++++++++++++:"+ effectStr);
        string[] str = effectStr.Split('-');
        string effectName = str[0];
        float effectTime = float.Parse(str[1]);

        GameObject go = null;
        if (unitEffectDict.TryGetValue(effectName, out go))
        {
            if (go != null)
            {
                AutoHideInSecond ahi = go.GetComponent<AutoHideInSecond>();
                if(ahi == null)
                {
                    ahi = go.AddComponent<AutoHideInSecond>();
                }
                ahi.show(effectTime);
            }
        }
    }

    private void OnAnimationEvent()
    {
        //Debug.LogError("OnAnimationEvent+++++++");
        isInAnimationEvent = true;
        int shortNameHash = animator.GetCurrentAnimatorStateInfo(0).shortNameHash;
        AnimationEventDelegate value;
        eventDelegates.TryGetValue(shortNameHash, out value);
        if (value != null)
        {
            value();
        }
        isInAnimationEvent = false;
    }

    public void PlayClip(int clip, AnimationEventDelegate eventDelegate = null)
    {
        if (eventDelegate != null)
        {
            eventDelegates[clip] = eventDelegate;
        }
        animator.Play(clip, 0, 0.0001f);
        //Debug.LogError("playClip-------------" + animator.GetCurrentAnimatorClipInfo(0));
        if (!isInAnimationEvent)
        {
            animator.Update(0f);
        }
        lastPlayClip = clip;
        if (Behaviour != null)
        {
            Behaviour.ClipChangingCallback(clip);
        }
    }

    public void PlayClipIfNotPlaying(int clip)
    {
        if (!IsPlayingClip(clip))
        {
            PlayClip(clip);
        }
    }

    public void ClearEventDelegate(int clip)
    {
        if (!eventDelegates.Remove(clip))
        {
            Debug.LogWarning("BattleUnit.ClearEventDelegate, event delegate not found for unit" + base.name);
        }
    }

    public bool IsPlayingClip(int clip)
    {
        return animator.GetCurrentAnimatorStateInfo(0).shortNameHash == clip;
    }

    public void BehaviourAdded()
    {
        behaviour = GetComponent<CommonBehaviour>();
        Behaviour.WasAttacked += OnUnitAttacked;
        Behaviour.Died += OnUnitDied;
        Behaviour.FreezeEffectChanged += OnFreezeChanged;
        int? num = lastPlayClip;
        if (num.HasValue)
        {
            Behaviour.ClipChangingCallback(lastPlayClip.Value);
        }
    }

    private void OnUnitAttacked(ICommonBehaviour dealer, ICommonBehaviour attacked, int damage, DamageType dmgType, int dmgDirX, AttackOptions options)
    {
        if (dmgType == DamageType.Fire && !(Behaviour == null) && GameController.Instance.FireManager.IsTargetBurning(Behaviour))
        {
            ColorController.UpdateFireColor();
            if (blackSmoke == null)
            {
                blackSmoke = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxBlackSmoke);
                blackSmoke.transform.SetParent(spriteContainer.transform, false);
                blackSmoke.transform.localPosition = blackSmokeOffset;
            }
        }
    }

    private void OnUnitDied(ICommonBehaviour behaviour, bool disintegrated)
    {
        Behaviour.Died -= OnUnitDied;
        if (Behaviour.Disintegrated)
        {
            KillSmoke();
            StopAllMoraleIndications();
            StartCoroutine(CoHideAndDestroy(CorpseManager.GetCorpseLifeTime(behaviour.CreatureType)));
        }
        else
        {
            MoveSmokeToCorpse();
            Behaviour.Destroy();
        }
    }

    private void OnFreezeChanged(ICommonBehaviour unit)
    {
        if (unit.IsFrozen)
        {
            KillSmoke();
        }
    }

    public void KillSmoke()
    {
        if (blackSmoke != null)
        {
            blackSmoke.Kill();
            blackSmoke = null;
        }
    }

    public void SetBurningDisintegation()
    {
        if (TrackingCreatureSpeed != null)
        {
            TrackingCreatureSpeed.ValueChanged -= OnSpeedChanged;
            TrackingCreatureSpeed = null;
        }
        ColorController.SetFireDisintegrationColor();
        //animator.runtimeAnimatorController = burningController;
        //烧死直接播死亡动画，不切换
        animator.Play("Death");
    }

    private void MoveSmokeToCorpse()
    {
        if (blackSmoke == null)
        {
            return;
        }
        blackSmoke.transform.SetParent(base.transform.parent, true);
        blackSmoke.DelayedKill(4.6f);
        CommonUnitBehaviour commonUnitBehaviour = Behaviour as CommonUnitBehaviour;
        if (commonUnitBehaviour != null)
        {
            Vector3 endValue = blackSmoke.transform.localPosition + FireManager.GetDeadFireOffset(commonUnitBehaviour.PositionDirectionOffsetOnDeath);
            float num = 0.5f;
            if (num >= 4.6f)
            {
                num = 4.5f;
            }
            blackSmoke.transform.DOLocalMove(endValue, num);
        }
    }

    public BattleUnitButton GetBattleUnitButtonInstance(float offsetY)
    {
        if (battleUnitButtonInstance == null)
        {
            battleUnitButtonInstance = UnityEngine.Object.Instantiate(battleUnitButtonPrefab);
            battleUnitButtonInstance.transform.SetParent(SpriteContainer.transform, false);
            battleUnitButtonInstance.transform.position = new Vector3(base.transform.position.x, base.transform.position.y + offsetY, 0f);
        }
        return battleUnitButtonInstance;
    }

    private IEnumerator CoHideAndDestroy(float delay)
    {
        yield return new WaitForSeconds(delay);
        if (animatorSprite != null)
        {
            ColorController.Hide(DestroyBattleUnit);
        }
        else
        {
            DestroyBattleUnit();
        }
    }

    private void DestroyBattleUnit()
    {
        if (behaviour != null)
        {
            behaviour.Destroy();
        }
        else
        {
            UnityEngine.Object.Destroy(base.gameObject);
        }
    }

    public void StartMoraleIndication(FearAndRageAnimation.AnimationType type)
    {
        if (fearAndRageAnimation == null)
        {
            fearAndRageAnimation = UnityEngine.Object.Instantiate(battleUnitEffects.FearAndRageAnimation, fearAnimationPlace, false);
        }
        fearAndRageAnimation.StartIndication(type);
    }

    public void StopMoraleIndication(FearAndRageAnimation.AnimationType type)
    {
        if (fearAndRageAnimation != null)
        {
            fearAndRageAnimation.StopIndication(type);
        }
    }

    public void StopAllMoraleIndications()
    {
        if (fearAndRageAnimation != null)
        {
            fearAndRageAnimation.StopAllIndications();
        }
    }

    public void DestroyCollider()
    {
        if (capsuleCollider != null)
        {
            capsuleCollider.enabled = false;
            UnityEngine.Object.Destroy(capsuleCollider);
            capsuleCollider = null;
        }
    }

    private void OnDrawGizmos()
    {
        if (capsuleCollider != null)
        {
            Gizmos.color = Color.white;
            Gizmos.DrawSphere(base.transform.position, capsuleCollider.radius);
        }
    }
}
