using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ZombieBehaviour : CommonUnitBehaviour
{
	private static int nameGenerator;

	private int clipResurrect;

	private int clipDeath;

	protected int ClipMove { get; set; }

	public bool InitedInBusZone { get; private set; }

	protected bool WaitingBeforeMoving { get; private set; }

	public bool FromFinalWave { get; private set; }

	protected int ClipResurrect
	{
		get
		{
			return clipResurrect;
		}
	}

	public void InitMeleeZombie(UnitTeam team, CreatureTemplate.CreatureType creatureType, BattleUnit battleUnit, int spawnLookDirectionX, CreatureTemplate.CreatureType resFrom = CreatureTemplate.CreatureType.None, bool waitingBeforeMoving = false, bool fromFinalWave = false)
	{
		InitCommonZombie(team, creatureType, battleUnit, spawnLookDirectionX, resFrom, waitingBeforeMoving, fromFinalWave);
		CustomZombieInit();
		if (!base.DenyStartMainCoroutineOnInit)
		{
			StartCoroutine(CoMain());
		}
	}

	protected virtual void CustomZombieInit()
	{
	}

	protected virtual void InitCommonZombie(UnitTeam team, CreatureTemplate.CreatureType creatureType, BattleUnit battleUnit, int spawnLookDirectionX, CreatureTemplate.CreatureType resFrom = CreatureTemplate.CreatureType.None, bool waitingBeforeMoving = false, bool fromFinalWave = false)
	{
		Init(team);
		string text = creatureType.ToString();
		base.name = text + "_" + ++nameGenerator;
		base.BattleUnit = battleUnit;
		Creature = new CreatureInstance(UnitsManager.Instance.GetCreature(creatureType), null);
		battleUnit.TrackSpeed(Creature.Speed);
		base.SpawnLookDirectionX = spawnLookDirectionX;
		base.ResurrectFrom = resFrom;
		base.Randomizer = UnitRandomizer.Create(this);
		InitedInBusZone = battleUnit.transform.position.x < -217f;
		WaitingBeforeMoving = waitingBeforeMoving && Creature.Template.IdleCount > 0;
		//Debug.LogError(string.Format("***Type:{0}, IdleCount:{1}", creatureType.ToString(), Creature.Template.IdleCount));
		FromFinalWave = fromFinalWave;
		InitAnimationClips();
		int moveDirectionX = GetMoveDirectionX();
		base.Targeter.Init(moveDirectionX, this, GetTargets(), false, Creature.Template.AoeRadius, Creature.Template.AoeHalfRadiusZ);
		base.Type = UnitType.Zombie;
		GameController.Instance.UnitRegister.RegisterUnit(this);
		base.UnitMover.Init(this, base.Targeter, null);
		CreatureTemplate.ApplyAnimOffsetY(creatureType, base.BattleUnit.Animator.transform);
		CreateHealthBar();
		if (SandEffect.Active)
		{
			base.Targeter.MoveDirectionSetEvent += base.CheckSlowdownBySandstorm;
			CheckSlowdownBySandstorm();
		}
	}

	protected override void InitAnimationClips()
	{
		base.InitAnimationClips();
		clipResurrect = base.BattleUnit.GetClip(ClipData.HashSpawn);
		ClipMove = base.BattleUnit.GetClip(ClipData.HashMove);
		base.ClipIdle = base.BattleUnit.GetClip(ClipData.HashIdle);
		clipDeath = base.BattleUnit.GetClip(ClipData.HashDeath);
	}

	private int GetMoveDirectionX()
	{
		switch (base.Team)
		{
		case UnitTeam.Left:
			return 1;
		case UnitTeam.Right:
			return -1;
		case UnitTeam.Neutral:
		{
			int directionToTarget;
			ICommonBehaviour commonBehaviour = base.Targeter.FindNearestTargetForNeutralZombieAttacker(this, GetTargets(), out directionToTarget);
			if (commonBehaviour != null)
			{
				return directionToTarget;
			}
			return (Random.Range(0, 2) == 0) ? 1 : (-1);
		}
		default:
			throw new UnityException();
		}
	}

	public override IEnumerable<ICommonBehaviour> GetTargets()
	{
		UnitRegister unitRegister = GameController.Instance.UnitRegister;
		return unitRegister.GetUnitsForOtherTeam(base.Team);
	}

	protected virtual IEnumerator CoBeforeMainLoop()
	{
		if (base.ResurrectFrom != 0)
		{
            //Debug.LogError("诈尸----------" + this.name + "," + SpawnLookDirectionX);
            BattleUnit bu = GetComponent<BattleUnit>();
            if (bu != null)
            {
                bu.showSpawn();
            }
            base.HealthBar.HiddenByPreparing = true;
			ResurrectActions();
			int stateHash = Animator.StringToHash("SpawnFrom_" + base.ResurrectFrom);
			int resClip = ((!base.BattleUnit.Animator.HasState(0, stateHash)) ? clipResurrect : stateHash);
			IEnumerator e = ProcessResurrect(resClip);
            while (e.MoveNext())
			{
				yield return e.Current;
			}
			base.HealthBar.HiddenByPreparing = false;
        }
        if (!IsDead)
		{
			base.Randomizer.UpdateVisualBonusEffect();
			if (WaitingBeforeMoving)
			{
				int multiplier = ((Random.Range(0, 2) != 0) ? 1 : (-1));
				base.BattleUnit.SpriteScale = new Vector3(base.Targeter.MoveDirectionX * multiplier, 1f, 1f);
				yield return CoWaitBeforeMoving();
			}
			else
			{
				base.BattleUnit.SpriteScale = new Vector3(base.Targeter.MoveDirectionX, 1f, 1f);
			}
		}
	}

	protected IEnumerator CoWaitBeforeMoving()
	{
		while (WaitingBeforeMoving && base.CanRest)
		{
			yield return CoRest();
			if (IsDead)
			{
				break;
			}
			BattleBus bus = GameController.Instance.BattleBus;
			BillBehaviour bill = ((!(bus != null)) ? null : bus.Bill);
			if (GameController.Instance.UnitRegister.HumanUnitHired || (bill != null && bill.DidFire))
			{
				WaitingBeforeMoving = false;
				base.BattleUnit.SpriteScale = new Vector3(base.Targeter.MoveDirectionX, 1f, 1f);
				break;
			}
		}
	}

	protected virtual IEnumerator CoMain()
	{
		IEnumerator e5 = CoBeforeMainLoop();
		while (e5.MoveNext())
		{
			yield return e5.Current;
		}
		while (!IsDead)
		{
			CheckTimelessWhileInMainCoroutine();
			if (base.Target != null && base.Target.IsDead)
			{
				ClearTarget();
			}
			if (base.UnitMover.ApplyChangeMoveDirByForcedTarget())
			{
				ClearTarget();
			}
			bool hasMeleeTarget;
			if (base.Target != null && base.IsAttackingMeleeTarget && base.Targeter.IsTargetInMeleeZone(base.Target, base.ExtraMoveX))
			{
				hasMeleeTarget = true;
			}
			else
			{
				ICommonBehaviour meleeTarget = base.Targeter.GetMeleeTarget(base.Target, base.ExtraMoveX);
				SetTarget(meleeTarget);
				hasMeleeTarget = meleeTarget != null;
			}
			if (hasMeleeTarget)
			{
				e5 = CoBeforeMeleeAttack(base.ClipIdle);
				while (e5.MoveNext())
				{
					yield return e5.Current;
				}
				if (IsDead)
				{
					break;
				}
				if (!base.Target.IsDead && base.Targeter.IsTargetInMeleeZone(base.Target, 0f))
				{
					e5 = CoAttack();
					while (e5.MoveNext())
					{
						yield return e5.Current;
					}
				}
			}
			else if (!base.CanRest || NeedWalk())
			{
				e5 = CoWalk();
				while (e5.MoveNext())
				{
					yield return e5.Current;
				}
				if (IsAwayFromBattlefield())
				{
					Destroy();
					break;
				}
			}
			else
			{
				e5 = CoRest();
				while (e5.MoveNext())
				{
					yield return e5.Current;
				}
			}
		}
	}

	protected virtual IEnumerator CoRest()
	{
		IEnumerator e = CoIdle(base.ClipIdle);
		while (e.MoveNext())
		{
			yield return e.Current;
		}
	}

	protected virtual IEnumerator CoAttack()
	{
		base.IsAttackingMeleeTarget = true;
		base.IsNextAttackCritical = Creature.MeleeDamage.CheckIfCritical();
		int clipAttack = ((base.ClipMeleeCritical == 0 || !base.IsNextAttackCritical) ? GetRandomMeleeAttackClip() : base.ClipMeleeCritical);
		base.BattleUnit.PlayClip(clipAttack, OnAttackTriggered);
		do
		{
			if (IsDead)
			{
				yield return WaitFor.FixedUpdate;
				break;
			}
			yield return WaitFor.FixedUpdate;
		}
		while (!base.BattleUnit.IsClipEnded);
		base.BattleUnit.ClearEventDelegate(clipAttack);
	}

	protected virtual void OnAttackTriggered()
	{
		Attack(base.IsNextAttackCritical);
	}

	protected virtual void OnWalkTriggered()
	{
	}

	protected virtual bool NeedWalk()
	{
		switch (GameController.Settings.Mode)
		{
		case GameController.Mode.PvP:
			return true;
		case GameController.Mode.Campaign:
			if (GameController.Settings.Mission.Id == 808 || base.IsInsideOwnBase)
			{
				return true;
			}
			break;
		case GameController.Mode.WallMart:
		case GameController.Mode.Xmas:
		case GameController.Mode.Rage:
		{
			float x = base.XYZ.x;
			Vector3 barricadePosition = GameController.BarricadePosition;
			if (x > barricadePosition.x)
			{
				return true;
			}
			break;
		}
		}
		int num = Random.Range(0, 100);
		//return true;//屏蔽idle的随机次数
		return base.Agility > num;
	}

	protected virtual IEnumerator CoWalk()
	{
		base.BattleUnit.PlayClip(ClipMove, OnWalkTriggered);
		while (!IsDead && base.Targeter.GetMeleeTarget(base.Target, base.ExtraMoveX) == null)
		{
			base.UnitMover.MoveForward();
			yield return WaitFor.FixedUpdate;
			if (base.BattleUnit.IsClipEnded)
			{
				break;
			}
		}
	}

	public override bool Attacked(ICommonBehaviour dealer, int damage, DamageType dmgType, int dmgDirX, AttackOptions options)
	{
		if (IsDead)
		{
			return false;
		}
		if (base.IsImmortal)
		{
			return false;
		}
		bool needSparkSpawn = NeedSparkSpawn(dmgType);
		int num = ApplyResistToDamage(dealer, dmgType, damage);
		bool isCritical = options.TryGet(AttackOption.Critical, false);
		bool flag = options.TryGet(AttackOption.DenySpawnDamageEffect, false);
		if (isCritical)
		{
			num *= 2;
			if (num > 0)
			{
				SpawnBloodSplashAndCritText(dmgDirX);
			}
		}
		int current = Creature.HealthPoints.Current;
		Creature.HealthPoints.Subtract(num, options.TryGet(AttackOption.DamagePastShield, false));
		if (options.TryGet(AttackOption.PlaySound, true))
		{
			if (dmgType == DamageType.Range)
			{
				SoundManager.Instance.TryPlayOn(delegate(SoundsBattle m)
				{
					m.RangeHit(dealer);
				});
			}
			else
			{
				SoundManager.Instance.TryPlayOn(delegate(SoundsBattle m)
				{
					m.MeleeHit(isCritical, dealer);
				});
			}
		}
		if (!flag)
		{
			SpawnDamageEffect(dealer, dmgDirX, num, dmgType, needSparkSpawn);
		}
		if (num > 0)
		{
			SpawnDamageText(num, dmgType, isCritical, dealer);
		}
		base.Attacked(dealer, damage, dmgType, dmgDirX, options);
		if (IsDead)
		{
			if (FxZombieMegaDamage.IsSuitableCreature(CreatureType) && FxZombieMegaDamage.IsSuitableDamage(dmgType) && (isCritical || FxZombieMegaDamage.IsStrongHit(current, num)))
			{
				MarkDisintegrated();
				// clipDeath = base.BattleUnit.GetClip("Empty");
				SpawnMegaDamage();
			}
			Die();
			return true;
		}
		base.UnitMover.CheckChangeMoveDirByForcedTarget(-dmgDirX, dealer);
		return false;
	}

	private void SpawnMegaDamage()
	{
		// FxZombieMegaDamage fxZombieMegaDamage = Object.Instantiate(GameController.Instance.BattleEffects.FxZombieMegaDamagePrefab, GameController.Instance.EffectsParent, false);
		// fxZombieMegaDamage.transform.position = base.XYZ;
		// fxZombieMegaDamage.transform.localScale = Scale;
	}

	protected void MakeFatalDamage(CommonUnitBehaviour target)
	{
		Debug.LogError("MakeFatalDamage++++++++++" + target);
		DamageType dmgType = DamageType.Explosion;
		int shieldCurrent = target.Creature.HealthPoints.ShieldCurrent;
		if (Creature.HealthPoints.ShieldCurrent > 0)
		{
			AttackManager.DoAttack(this, target, shieldCurrent, dmgType, base.Targeter.MoveDirectionX);
		}
		shieldCurrent = target.Creature.HealthPoints.Current;
		AttackManager.DoAttack(this, target, shieldCurrent, dmgType, base.Targeter.MoveDirectionX);
	}

	protected void ToggleSpecialGlow(bool enable)
	{
		if (enable)
		{
			if (base.BattleUnit != null && !IsDead)
			{
				base.BattleUnit.StartMoraleIndication(FearAndRageAnimation.AnimationType.ZombieSpecial);
			}
		}
		else if (base.BattleUnit != null)
		{
			base.BattleUnit.StopMoraleIndication(FearAndRageAnimation.AnimationType.ZombieSpecial);
		}
	}

	protected override void Die()
	{
		Debug.LogError("这货挂了===========");
		base.Die();
		StartCoroutine(CoDie(clipDeath));
		PlayDeathSound();
	}

	protected virtual void PlayDeathSound()
	{
		if (UnitsManager.IsBigZombie(CreatureType))
		{
			SoundManager.Instance.TryPlayOn(delegate(SoundsBattle m)
			{
				m.BigZombieDeath();
			});
		}
		else
		{
			SoundManager.Instance.TryPlayOn(delegate(SoundsBattle m)
			{
				m.CommonZombieDeath();
			});
		}
	}

	public override void SpawnerInitialization(Spawner.SpawnInfo info)
	{
		base.SpawnerInitialization(info);
		InitMeleeZombie(info.Team, info.RealType, info.BattleUnit, info.SpawnLookDirectionX, info.ResurrectFrom, info.FromStartWave, info.FromFinalWave);
	}
}
