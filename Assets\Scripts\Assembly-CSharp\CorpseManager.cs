using System;
using System.Collections.Generic;
using UnityEngine;

public class CorpseManager : IDisposable
{
	public enum Decal
	{
		Death,
		Spawn,
		AfterMelee
	}

	public class ResurrectInfo
	{
		public CreatureTemplate.CreatureType ResurrectTo { get; private set; }

		public int MaxResurrectCount { get; private set; }

		public ResurrectTime CorpseResurrectTime { get; private set; }

		public static ResurrectInfo Create(CreatureTemplate.CreatureType resurrectTo)
		{
			return Create(resurrectTo, 1, new ResurrectTime());
		}

		public static ResurrectInfo Create(CreatureTemplate.CreatureType resurrectTo, int maxResurrectCount)
		{
			return Create(resurrectTo, maxResurrectCount, new ResurrectTime());
		}

		public static ResurrectInfo Create(CreatureTemplate.CreatureType resurrectTo, ResurrectTime corpseResurrectTime)
		{
			return Create(resurrectTo, 1, corpseResurrectTime);
		}

		public static ResurrectInfo Create(CreatureTemplate.CreatureType resurrectTo, int maxResurrectCount, ResurrectTime corpseResurrectTime)
		{
			ResurrectInfo resurrectInfo = new ResurrectInfo();
			resurrectInfo.ResurrectTo = resurrectTo;
			resurrectInfo.MaxResurrectCount = maxResurrectCount;
			resurrectInfo.CorpseResurrectTime = corpseResurrectTime;
			return resurrectInfo;
		}
	}

	public class ResurrectTime
	{
		public const float DefaultCorpseLifeTime = 10f;

		private const float DefaultRandomDelta = 1f;

		public float CorpseLifeTime { get; private set; }

		public float CorpseLifeTimeDelta { get; private set; }

		public ResurrectTime()
		{
			CorpseLifeTime = 10f;
			CorpseLifeTimeDelta = 1f;
		}

		public ResurrectTime(float corpseLifeTime, float corpseLifeTimeDelta)
		{
			CorpseLifeTime = corpseLifeTime;
			CorpseLifeTimeDelta = corpseLifeTimeDelta;
		}

		public float GetRandomCorpseLifeTime()
		{
			return CorpseLifeTime + UnityEngine.Random.Range(0f - CorpseLifeTimeDelta, CorpseLifeTimeDelta);
		}
	}

	private static Dictionary<CreatureTemplate.CreatureType, ResurrectInfo> resurrectInfos = new Dictionary<CreatureTemplate.CreatureType, ResurrectInfo>
	{
		{
			CreatureTemplate.CreatureType.h_naked,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_naked)
		},
		{
			CreatureTemplate.CreatureType.h_glenn,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.h_glenn2)
		},
		{
			CreatureTemplate.CreatureType.h_glenn2,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke, 2)
		},
		{
			CreatureTemplate.CreatureType.h_puke,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.h_builder,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_builder)
		},
		{
			CreatureTemplate.CreatureType.h_chopper,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_chopper)
		},
		{
			CreatureTemplate.CreatureType.h_farmer,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.h_charlotte,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_girl)
		},
		{
			CreatureTemplate.CreatureType.h_carlos,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.h_cop,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_cop)
		},
		{
			CreatureTemplate.CreatureType.h_cheechmarin,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_cheechmarin)
		},
		{
			CreatureTemplate.CreatureType.h_firefighter,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_firefighter)
		},
		{
			CreatureTemplate.CreatureType.h_jailer,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.z_undead,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_undead, 5)
		},
		{
			CreatureTemplate.CreatureType.h_jailerrifle,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.h_medic,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_witch)
		},
		{
			CreatureTemplate.CreatureType.h_judi,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_girl)
		},
		{
			CreatureTemplate.CreatureType.h_saw,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_monk, new ResurrectTime(0f, 0f))
		},
		{
			CreatureTemplate.CreatureType.h_guard,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_undead)
		},
		{
			CreatureTemplate.CreatureType.h_berserker,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_naked)
		},
		{
			CreatureTemplate.CreatureType.b_naked,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_naked)
		},
		{
			CreatureTemplate.CreatureType.b_farmer,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.b_cap,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.b_builder,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_undead)
		},
		{
			CreatureTemplate.CreatureType.b_biker,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.h_sonya,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_witch)
		},
		{
			CreatureTemplate.CreatureType.h_mechanic,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.h_sniper,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_girl)
		},
		{
			CreatureTemplate.CreatureType.z_soldier,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_soldier)
		},
		{
			CreatureTemplate.CreatureType.h_lester,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.h_hero,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_soldier)
		},
		{
			CreatureTemplate.CreatureType.h_heavyGuard,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_heavyGuard)
		},
		{
			CreatureTemplate.CreatureType.z_prisoner,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_preAlien, 2, new ResurrectTime(0f, 0f))
		},
		{
			CreatureTemplate.CreatureType.h_austin,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.h_lightSoldier,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_soldier)
		},
		{
			CreatureTemplate.CreatureType.h_agent,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.h_agent002,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_puke)
		},
		{
			CreatureTemplate.CreatureType.h_carol,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_girl)
		},
		{
			CreatureTemplate.CreatureType.h_toadstool,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_witch)
		},
		{
			CreatureTemplate.CreatureType.h_queen,
			ResurrectInfo.Create(CreatureTemplate.CreatureType.z_girl)
		}
	};

	private UnitRegister unitRegister;

	private List<FxCreatureCorpse> corpses = new List<FxCreatureCorpse>();

	private Transform corpsesHolder;

	public bool HasCorpses
	{
		get
		{
			return corpses.Count > 0;
		}
	}

	public CorpseManager(UnitRegister unitRegister)
	{
		this.unitRegister = unitRegister;
		unitRegister.UnitDied += OnUnitDied;
	}

	public static ResurrectInfo GetResurrectingInfo(CreatureTemplate.CreatureType creature)
	{
		if (!IsResurrectingCreature(creature))
		{
			throw new Exception("Trying get resurrecting info for non resurrecting creature: " + creature);
		}
		return resurrectInfos[creature];
	}

	public static float GetCorpseLifeTime(CreatureTemplate.CreatureType creature)
	{
		if (!IsResurrectingCreature(creature))
		{
			return 10f;
		}
		return resurrectInfos[creature].CorpseResurrectTime.CorpseLifeTime;
	}

	public static bool IsResurrectingCreature(CreatureTemplate.CreatureType creature)
	{
		return resurrectInfos.ContainsKey(creature);
	}

	public static bool IsResurrectingToHuman(CreatureTemplate.CreatureType creature)
	{
		ResurrectInfo value;
		if (resurrectInfos.TryGetValue(creature, out value))
		{
			return UnitsManager.IsHumanPersonOrUnCollectibleHuman(value.ResurrectTo);
		}
		return false;
	}

	public static bool IsUndead(CreatureTemplate.CreatureType creature)
	{
		ResurrectInfo value;
		if (resurrectInfos.TryGetValue(creature, out value))
		{
			return value.ResurrectTo == creature;
		}
		return false;
	}

	public static UnitTeam GetSpawnTeamPVPMission(CreatureTemplate.CreatureType spawnType, UnitTeam prevTeam)
	{
		if (UnitsManager.IsHumanPersonOrUnCollectibleHuman(spawnType))
		{
			return prevTeam;
		}
		return UnitTeam.Neutral;
	}

	public static UnitTeam GetSpawnTeamCampaignMission(CreatureTemplate.CreatureType spawnType)
	{
		return (!GameController.Settings.Mission.MainWaveHasBandits) ? GetSpawnTeamCommonMission(spawnType) : GetSpawnTeamBanditsMission(spawnType);
	}

	public static UnitTeam GetSpawnTeamCommonMission(CreatureTemplate.CreatureType spawnType)
	{
		return UnitsManager.IsHumanPersonOrUnCollectibleHuman(spawnType) ? UnitTeam.Left : UnitTeam.Right;
	}

	public static UnitTeam GetSpawnTeamBanditsMission(CreatureTemplate.CreatureType spawnType)
	{
		return UnitsManager.IsHumanPersonOrUnCollectibleHuman(spawnType) ? UnitTeam.Left : UnitTeam.Neutral;
	}

	public void Dispose()
	{
		unitRegister.UnitDied -= OnUnitDied;
	}

	private void OnUnitDied(ICommonBehaviour unit)
	{
		if ((unit is CommonHumanBehaviour || unit is ZombieBehaviour) && !(unit as CommonUnitBehaviour).Disintegrated)
		{
			SpawnCorpse((CommonUnitBehaviour)unit);
		}
	}

	public bool UnitIgnoresFatalDamageDirection(CreatureTemplate.CreatureType unit)
	{
		if (unit == CreatureTemplate.CreatureType.h_specops || unit == CreatureTemplate.CreatureType.z_cop || unit == CreatureTemplate.CreatureType.z_cephalopoda)
		{
			return true;
		}
		return false;
	}

	private Sprite GetDecal(CreatureTemplate.CreatureType type, Decal decalType)
	{
		UnitDataScriptableObject unitDataScriptableObject = GameController.Instance.AssetBundleLoaderUnitData.LoadBundle(type);
		Sprite result = null;
		if (unitDataScriptableObject != null)
		{
			switch (decalType)
			{
			case Decal.Death:
				result = unitDataScriptableObject.SpriteAfterDeath;
				break;
			case Decal.AfterMelee:
				result = unitDataScriptableObject.SpriteAfterMelee;
				break;
			case Decal.Spawn:
				result = unitDataScriptableObject.SpriteAfterSpawn;
				break;
			}
		}
		return result;
	}

	private bool IsResurrectingUnit(CreatureTemplate.CreatureType unitId)
	{
		switch (unitId)
		{
		case CreatureTemplate.CreatureType.z_undead:
			return true;
		case CreatureTemplate.CreatureType.h_ballisticShielder:
			return false;
		case CreatureTemplate.CreatureType.h_soldierCap:
			return false;
		case CreatureTemplate.CreatureType.h_specops:
			return false;
		case CreatureTemplate.CreatureType.z_soldier:
			return true;
		case CreatureTemplate.CreatureType.z_prisoner:
			return true;
		case CreatureTemplate.CreatureType.h_swat:
			return false;
		default:
			return !UnitsManager.IsZombie(unitId);
		}
	}

	private int GetCorpseSpriteScaleX(CreatureTemplate.CreatureType type, int moveDirectionX, int fatalDirectionX)
	{
		if (UnitIgnoresFatalDamageDirection(type))
		{
			return moveDirectionX;
		}
		return -fatalDirectionX;
	}

	private void SpawnCorpse(CommonUnitBehaviour unit)
	{
		CreatureTemplate.CreatureType spawnType = CreatureTemplate.CreatureType.None;
		int resurrectCounter = unit.ResurrectCounter + 1;
		float corpseLifeTime = 10f;
		CreatureTemplate.CreatureType resurrectFrom = unit.CreatureType;
		UnitTeam oldTeam = unit.Team;
		// Debug.LogError($"躺尸--------------{unit.CreatureType},{oldTeam}");
		// if (oldTeam != UnitTeam.Left)
		// {
		// 	Debug.LogError("敌人死了直接消失");
		// 	unit.BattleUnit.DoFadeOut();
		// 	return;
		// }
		if (IsResurrectingUnit(unit.CreatureType))
		{
			ResurrectInfo resurrectingInfo = GetResurrectingInfo(unit.VisualType);
			spawnType = resurrectingInfo.ResurrectTo;
			corpseLifeTime = resurrectingInfo.CorpseResurrectTime.GetRandomCorpseLifeTime();
			if (resurrectCounter > resurrectingInfo.MaxResurrectCount)
			{
				spawnType = CreatureTemplate.CreatureType.None;
			}
			if (GameController.Settings.Mode == GameController.Mode.PvP && spawnType != 0)
			{
				UnitTeam spawnTeamPVPMission = GetSpawnTeamPVPMission(spawnType, oldTeam);
				if (oldTeam != UnitTeam.Neutral && spawnTeamPVPMission == oldTeam)
				{
					corpseLifeTime = 0.001f;
				}
			}
		}
		FxCreatureCorpse corpse = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxCreatureCorpse);
		corpse.CorpseLifeTimeEnded += delegate(FxCreatureCorpse c)
		{
			OnCorpseLifeTimeEnded(c, oldTeam, unit.VisualType, spawnType, corpse.SpriteBlendColor, resurrectCounter, resurrectFrom, unit.Randomizer.CurrentBonus);
		};
		corpse.transform.SetParent(GetCorpsesHolder(), false);
		corpse.transform.localPosition = unit.XYZ;
		float x = GetCorpseSpriteScaleX(unit.VisualType, unit.Targeter.MoveDirectionX, unit.FatalDamageDirectionX);
		corpse.Scale = new Vector3(x, 1f, 1f);
		corpses.Add(corpse);
		if (unit.BattleUnit != null && unit.BattleUnit.Animator != null)
		{
			SpriteRenderer component = unit.BattleUnit.Animator.GetComponent<SpriteRenderer>();
			if (component != null)
			{
				corpse.SpriteBlendColor = unit.BattleUnit.ColorController.GetRealBlendColor();
			}
		}
		corpse.Init(unit.VisualType, corpseLifeTime, x);
	}

	private void OnCorpseLifeTimeEnded(FxCreatureCorpse corpse, UnitTeam corpseTeam, CreatureTemplate.CreatureType corpseType, CreatureTemplate.CreatureType spawnType, Color color, int resurrectCounter, CreatureTemplate.CreatureType resurrectFrom, UnitRandomizer.Bonus bonus)
	{
		if (!corpses.Remove(corpse))
		{
			throw new Exception("Corpse not found " + corpseType);
		}
		float x = corpse.transform.position.x;
		float z = corpse.transform.position.z;
		int num = ((corpse.Scale.x > 0f) ? 1 : (-1));
		bool flag = false;
		if (spawnType == CreatureTemplate.CreatureType.None)
		{
			flag = true;
		}
		else if (GameController.Instance.LeftTeam.BattleBus != null)
		{
			CreatureTemplate.CreatureType realType = UnitsManager.GetRealType(spawnType);
			CreatureTemplate creature = UnitsManager.Instance.GetCreature(realType);
			if (creature == null)
			{
				throw new Exception("Not found template for: " + spawnType);
			}
			float num2 = creature.SpawnShift * (float)num;
			flag = GameController.Instance.LeftTeam.BattleBus.IsInsideBus(x + num2, z);
		}
		if (flag)
		{
			corpse.Hide(true);
			return;
		}
		UnitTeam team;
		switch (GameController.Settings.Mode)
		{
		case GameController.Mode.Campaign:
			team = GetSpawnTeamCampaignMission(spawnType);
			break;
		case GameController.Mode.PvP:
			team = GetSpawnTeamPVPMission(spawnType, corpseTeam);
			break;
		case GameController.Mode.WallMart:
		case GameController.Mode.Xmas:
		case GameController.Mode.Rage:
			team = GetSpawnTeamCommonMission(spawnType);
			break;
		case GameController.Mode.CornFarm:
			team = GetSpawnTeamBanditsMission(spawnType);
			break;
		default:
			team = UnitTeam.None;
			break;
		}
		Spawner spawner = GameController.Instance.GetTeam(team).Spawner;
		int num3 = -num;
		float startXPos = x;
		float posZ = z;
		int spawnLookDirectionX = num3;
		spawner.SpawnUnit(spawnType, startXPos, posZ, spawnLookDirectionX, color, resurrectFrom, resurrectCounter, false, false, bonus);
		SpawnDecal(corpseType, Decal.Death, corpse.transform.position, num);
		corpse.Hide(false);
	}

	public void SpawnDecal(CreatureTemplate.CreatureType unitType, Decal decalType, Vector3 position, float direction, Sprite customDecalSprite = null)
	{
		Sprite sprite = customDecalSprite ?? GetDecal(unitType, decalType);
		if (sprite != null)
		{
			if (decalType == Decal.Death || decalType == Decal.Spawn)
			{
				FxDecal fxDecal = SpawnDecal(sprite, position, direction, null, true);
				float offsetX;
				float offsetY;
				float offsetZ;
				CreaturesOffsets.GetFootOffsetY(unitType, out offsetX, out offsetY, out offsetZ);
				Vector3 position2 = fxDecal.transform.position;
				position2.y -= offsetY;
				fxDecal.transform.position = position2;
			}
			else
			{
				FxDecal fxDecal2 = SpawnDecal(sprite, position, direction);
				CreatureTemplate.ApplyAnimOffsetY(unitType, fxDecal2.SpriteRenderer.transform);
			}
		}
	}

	public FxDecal SpawnDecal(Sprite decalSprite, Vector3 position, float direction, Vector3? spriteLocalPosition = null, bool isTimeOrderDecal = false)
	{
		FxDecal fxDecal = null;
		if (isTimeOrderDecal)
		{
			fxDecal = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxTimeOrderDecal);
			fxDecal.transform.position = position;
			Vector3 position2 = fxDecal.transform.position;
			position2.y += ApplySpriteOffsetY.Get(position.z);
			fxDecal.transform.position = position2;
			fxDecal.Init(decalSprite, 5f, 2f, null);
		}
		else
		{
			fxDecal = UnityEngine.Object.Instantiate(GameController.Instance.BattleEffects.FxDecal);
			fxDecal.transform.SetParent(GameController.Instance.DecalsRoot, false);
			fxDecal.transform.position = position;
			fxDecal.Init(decalSprite, 5f, 2f, 10f);
		}
		fxDecal.Scale = new Vector3(direction, 1f, 1f);
		if (spriteLocalPosition.HasValue)
		{
			fxDecal.SpriteRenderer.transform.localPosition = spriteLocalPosition.Value;
		}
		return fxDecal;
	}

	private Transform GetCorpsesHolder()
	{
		if (object.ReferenceEquals(corpsesHolder, null))
		{
			corpsesHolder = new GameObject("Corpses").transform;
		}
		return corpsesHolder;
	}

	public IEnumerable<Vector3> GetCorpsePositions()
	{
		foreach (FxCreatureCorpse corpse in corpses)
		{
			yield return corpse.transform.position;
		}
	}

	public void FadeAllCorpses()
	{
		for (int num = corpses.Count - 1; num >= 0; num--)
		{
			corpses[num].Hide(true);
		}
		corpses.Clear();
	}
}
