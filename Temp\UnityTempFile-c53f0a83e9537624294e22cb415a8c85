/target:library
/nowarn:0169
/nowarn:0649
/out:Temp/Assembly-CSharp-Editor.dll
/debug:portable
/optimize-
/nostdlib+
/preferreduilang:en-US
/langversion:latest
/reference:Library/ScriptAssemblies/Assembly-CSharp.dll
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.AIModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.ARModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.AnimationModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.AudioModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.BaselibModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.ClothModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.CoreModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.DirectorModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.FileSystemHttpModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.GridModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.InputModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.SpatialTrackingModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.StreamingModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.StyleSheetsModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.TLSModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.TerrainModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.TextCoreModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.TilemapModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.TimelineModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UIModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UNETModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UmbraModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.VFXModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.VRModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.VideoModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.WindModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.XRModule.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEditor.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/Unity.Locator.dll"
/reference:Library/ScriptAssemblies/CFXRRuntime.dll
/reference:Library/ScriptAssemblies/CFXREditor.dll
/reference:Library/ScriptAssemblies/UnityEngine.Purchasing.dll
/reference:Library/ScriptAssemblies/UnityEditor.Purchasing.dll
/reference:Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll
/reference:Library/ScriptAssemblies/CFXRDemo.dll
/reference:Library/ScriptAssemblies/AllIn1VfxDemoScriptAssemblies.dll
/reference:Library/ScriptAssemblies/Unity.PackageManagerUI.Editor.dll
/reference:Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll
/reference:Library/ScriptAssemblies/FR2.dll
/reference:Library/ScriptAssemblies/AllIn1SpriteShaderAssembly.dll
/reference:Library/ScriptAssemblies/AllIn1VfxAssmebly.dll
/reference:Library/ScriptAssemblies/CFXRDemoEditor.dll
/reference:Library/ScriptAssemblies/Unity.TextMeshPro.dll
/reference:Library/ScriptAssemblies/Unity.Analytics.DataPrivacy.dll
/reference:Library/ScriptAssemblies/AllIn1VfxTexDemoAssembly.dll
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/GUISystem/UnityEngine.UI.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/GUISystem/Editor/UnityEditor.UI.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/TestRunner/Editor/UnityEditor.TestRunner.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/TestRunner/UnityEngine.TestRunner.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/TestRunner/net35/unity-custom/nunit.framework.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/Timeline/RuntimeEditor/UnityEngine.Timeline.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/Timeline/Editor/UnityEditor.Timeline.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/Networking/UnityEngine.Networking.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/Networking/Editor/UnityEditor.Networking.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/UnityGoogleAudioSpatializer/Editor/UnityEditor.GoogleAudioSpatializer.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/UnityGoogleAudioSpatializer/RuntimeEditor/UnityEngine.GoogleAudioSpatializer.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/UnitySpatialTracking/Editor/UnityEditor.SpatialTracking.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/UnitySpatialTracking/RuntimeEditor/UnityEngine.SpatialTracking.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/UnityVR/Editor/UnityEditor.VR.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
/reference:"C:/Program Files/Unity/2018.4.33f1/Editor/Data/PlaybackEngines/windowsstandalonesupport/UnityEditor.WindowsStandalone.Extensions.dll"
/reference:Assets/ConsolePro/Editor/ConsolePro.Editor.dll
/reference:Assets/Plugins/AWSSDK.CognitoIdentity.dll
/reference:Assets/Plugins/AWSSDK.CognitoSync.dll
/reference:Assets/Plugins/AWSSDK.Core.dll
/reference:Assets/Plugins/AWSSDK.KinesisFirehose.dll
/reference:Assets/Plugins/AWSSDK.SecurityToken.dll
/reference:Assets/Plugins/CSSDK.dll
/reference:Assets/Plugins/DOTween.dll
/reference:Assets/Plugins/DOTween46.dll
/reference:Assets/Plugins/DOTween50.dll
/reference:Assets/Plugins/ICSharpCode.SharpZipLib.dll
/reference:Assets/Plugins/Mono.Data.Sqlite.dll
/reference:Assets/Plugins/Newtonsoft.Json.dll
/reference:D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/PackageCache/com.unity.ads@2.0.8/Editor/UnityEditor.Advertisements.dll
/reference:D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/PackageCache/com.unity.analytics@3.2.3/AnalyticsStandardEvents/Unity.Analytics.StandardEvents.dll
/reference:D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/PackageCache/com.unity.analytics@3.2.3/Editor/Unity.Analytics.Editor.dll
/reference:D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/PackageCache/com.unity.analytics@3.2.3/Editor/Unity.Analytics.Tracker.dll
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\mscorlib.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Core.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Runtime.Serialization.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Xml.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Xml.Linq.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Numerics.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Numerics.Vectors.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Net.Http.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Microsoft.CSharp.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Data.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\Microsoft.Win32.Primitives.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\netstandard.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.AppContext.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.Concurrent.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.NonGeneric.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.Specialized.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.Annotations.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.EventBasedAsync.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.Primitives.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.TypeConverter.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Console.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Data.Common.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Contracts.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Debug.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.FileVersionInfo.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Process.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.StackTrace.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.TextWriterTraceListener.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Tools.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.TraceSource.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Drawing.Primitives.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Dynamic.Runtime.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Globalization.Calendars.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Globalization.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Globalization.Extensions.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.Compression.ZipFile.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.DriveInfo.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.Primitives.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.Watcher.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.IsolatedStorage.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.MemoryMappedFiles.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.Pipes.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.UnmanagedMemoryStream.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.Expressions.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.Parallel.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.Queryable.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Http.Rtc.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.NameResolution.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.NetworkInformation.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Ping.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Primitives.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Requests.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Security.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Sockets.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.WebHeaderCollection.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.WebSockets.Client.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.WebSockets.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ObjectModel.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Emit.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Emit.ILGeneration.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Emit.Lightweight.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Extensions.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Primitives.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Resources.Reader.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Resources.ResourceManager.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Resources.Writer.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.CompilerServices.VisualC.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Extensions.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Handles.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.InteropServices.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Numerics.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Formatters.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Json.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Primitives.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Xml.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Claims.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Algorithms.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Csp.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Encoding.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Primitives.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.X509Certificates.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Principal.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.SecureString.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Duplex.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Http.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.NetTcp.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Primitives.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Security.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Text.Encoding.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Text.Encoding.Extensions.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Text.RegularExpressions.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Overlapped.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Tasks.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Tasks.Parallel.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Thread.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.ThreadPool.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Timer.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ValueTuple.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.ReaderWriter.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XDocument.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XmlDocument.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XmlSerializer.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XPath.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XPath.XDocument.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\unityscript\UnityScript.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\unityscript\UnityScript.Lang.dll"
/reference:"C:\Program Files\Unity\2018.4.33f1\Editor\Data\MonoBleedingEdge\lib\mono\unityscript\Boo.Lang.dll"
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2018_4_33
/define:UNITY_2018_4
/define:UNITY_2018
/define:PLATFORM_ARCH_64
/define:UNITY_64
/define:UNITY_INCLUDE_TESTS
/define:UNITY_ANALYTICS
/define:ENABLE_AUDIO
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_DUCK_TYPING
/define:ENABLE_MICROPHONE
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_PHYSICS
/define:ENABLE_SPRITES
/define:ENABLE_GRID
/define:ENABLE_TILEMAP
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_DIRECTOR
/define:ENABLE_UNET
/define:ENABLE_LZMA
/define:ENABLE_UNITYEVENTS
/define:ENABLE_WEBCAM
/define:ENABLE_WWW
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_HUB
/define:ENABLE_CLOUD_PROJECT_ID
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_EDITOR_HUB
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_TIMELINE
/define:ENABLE_EDITOR_METRICS
/define:ENABLE_EDITOR_METRICS_CACHING
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:INCLUDE_DYNAMIC_GI
/define:INCLUDE_GI
/define:ENABLE_MONO_BDWGC
/define:PLATFORM_SUPPORTS_MONO
/define:RENDER_SOFTWARE_CURSOR
/define:INCLUDE_PUBNUB
/define:ENABLE_VIDEO
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_LOCALIZATION
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_STANDALONE
/define:ENABLE_SUBSTANCE
/define:ENABLE_RUNTIME_GI
/define:ENABLE_MOVIES
/define:ENABLE_NETWORK
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_VR
/define:ENABLE_AR
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_MONO
/define:NET_4_6
/define:ENABLE_PROFILER
/define:DEBUG
/define:TRACE
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_BURST_AOT
/define:UNITY_TEAM_LICENSE
/define:UNITY_PRO_LICENSE
/define:_SELF_DEBUG
/define:_NO_ASB
/define:_NO_NET
/define:Change_Res_3D1
/define:USE_CAR_3D
/define:CSHARP_7_OR_LATER
/define:CSHARP_7_3_OR_NEWER
Assets\Editor\Clear.cs
Assets\Editor\EditSprite.cs
Assets\Editor\FindImageReferences.cs
Assets\Editor\MapPrefabHolderEditor.cs
"Assets\JMO Assets\Cartoon FX (legacy)\Editor\CFX_InspectorHelpEditor.cs"
"Assets\JMO Assets\Cartoon FX (legacy)\Editor\CFX_SpawnSystemEditor.cs"
"Assets\JMO Assets\Cartoon FX Easy Editor\Editor\CFXEasyEditor.cs"
"Assets\Lux Lit Particles\Scripts\Editor\LuxParticles_FadeDistancesDrawer.cs"
"Assets\Lux Lit Particles\Scripts\Editor\LuxParticles_HelpDrawer.cs"
